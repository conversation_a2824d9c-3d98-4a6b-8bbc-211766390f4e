<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Observer;

use Comave\Sales\Api\PostProcessingMessageInterface;
use Comave\Sales\Api\PostProcessingMessageInterfaceFactory;
use Comave\Sales\Model\ProcessorTypeManager;
use Comave\Sales\Model\Queue\Consumer\PlaceOrderPostProcessing;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\MessageQueue\PublisherInterface;

readonly class CheckoutSubmitAllAfter implements ObserverInterface
{
    /**
     * @param \Comave\Sales\Api\PostProcessingMessageInterfaceFactory $messageFactory
     * @param \Magento\Framework\MessageQueue\PublisherInterface $publisher
     */
    public function __construct(
        private PostProcessingMessageInterfaceFactory $messageFactory,
        private PublisherInterface $publisher
    ) {
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $order = $observer->getEvent()->getOrder();

        /**
         * @var PostProcessingMessageInterface $sellerMessage
         */
        $sellerMessage = $this->messageFactory->create();
        $sellerMessage->setOrderId($order->getEntityId())->setProcessingType(
            ProcessorTypeManager::SELLER_EMAIL_TYPE_PROCESSOR
        );
        $this->publisher->publish(PlaceOrderPostProcessing::SELLER_EMAIL_TOPIC_NAME, $sellerMessage);
    }
}