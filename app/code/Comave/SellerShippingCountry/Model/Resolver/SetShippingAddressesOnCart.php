<?php

namespace Comave\SellerShippingCountry\Model\Resolver;

use Magento\Framework\App\ObjectManager;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Model\QuoteIdToMaskedQuoteIdInterface;
use Magento\Quote\Model\QuoteRepository;
use Magento\QuoteGraphQl\Model\Cart\AssignShippingAddressToCart;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;
use Magento\QuoteGraphQl\Model\Cart\GetShippingAddress;
use Magento\QuoteGraphQl\Model\Cart\SetShippingAddressesOnCart as OriginalSetShippingAddressesOnCart;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\GraphQl\Model\Query\Resolver\ContextInterface;
use Magento\Quote\Api\Data\AddressInterfaceFactory;
use Magento\QuoteGraphQl\Model\Cart\SetShippingAddressesOnCart as SetShippingAddressesOnCartInterface;
use Comave\SellerShippingCountry\Helper\Data as ShippingRestrictionHelper;
use Webkul\Marketplace\Helper\Data;

class SetShippingAddressesOnCart extends OriginalSetShippingAddressesOnCart
{
    protected ShippingRestrictionHelper $shippingRestrictionHelper;

    /** @var \Webkul\Marketplace\Helper\Data */
    protected $marketplaceHelper;

    /**
     * @param ShippingRestrictionHelper $shippingRestrictionHelper
     * @param QuoteIdToMaskedQuoteIdInterface $quoteIdToMaskedQuoteId
     * @param GetCartForUser $getCartForUser
     * @param AssignShippingAddressToCart $assignShippingAddressToCart
     * @param GetShippingAddress $getShippingAddress
     * @param Data $marketplaceHelper
     * @param QuoteRepository|null $quoteRepository
     */
    public function __construct(
        ShippingRestrictionHelper $shippingRestrictionHelper,
        QuoteIdToMaskedQuoteIdInterface $quoteIdToMaskedQuoteId,
        GetCartForUser $getCartForUser,
        AssignShippingAddressToCart $assignShippingAddressToCart,
        GetShippingAddress $getShippingAddress,
        \Webkul\Marketplace\Helper\Data $marketplaceHelper,
        QuoteRepository $quoteRepository = null
    ) {
        $this->shippingRestrictionHelper = $shippingRestrictionHelper;
        $this->marketplaceHelper = $marketplaceHelper;

        parent::__construct($quoteIdToMaskedQuoteId, $getCartForUser, $assignShippingAddressToCart, $getShippingAddress, $quoteRepository);
    }

    public function execute(\Magento\GraphQl\Model\Query\ContextInterface $context, CartInterface $cart, array $shippingAddressesInput): void
    {
        foreach ($shippingAddressesInput as $addressInput) {
            //TODO: consider multishipping/multiple addresses edge cases - how to treat them ?
            $countryCode = $addressInput['country_code'] ?? ($addressInput['address']['country_code'] ?? null);

            foreach ($cart->getItems() as $item) {
                $sellerId = $this->marketplaceHelper->getSellerIdByProductId($item->getProduct()->getId());
                if (!$sellerId) {
                    continue;
                }

                if ($this->shippingRestrictionHelper->isCountryRestricted($sellerId, $countryCode)) {
                    $itemDesc = $item->getName() . ' [' . $item->getSku() . ']';
                    throw new GraphQlInputException(__("Item %1 cannot be shipped to selected country (%2)", $itemDesc, $countryCode));
                }
            }
        }

        parent::execute($context, $cart, $shippingAddressesInput);
    }
}
