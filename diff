 
0 / 16 files viewed
Filter changed files
   2 changes: 0 additions & 2 deletions2  
app/code/Comave/Sales/view/adminhtml/ui_component/sales_order_grid.xml
Viewed
Original file line number	Diff line number	Diff line change
@@ -6,7 +6,6 @@
            <settings>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
                <dataType>text</dataType>
                <align>left</align>
                <sortable>false</sortable>
                <label translate="true">SKU</label>
                <filter>text</filter>
@@ -16,7 +15,6 @@
            <settings>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
                <dataType>text</dataType>
                <align>left</align>
                <sortable>false</sortable>
                <label translate="true">EAN</label>
                <filter>text</filter>
  50 changes: 50 additions & 0 deletions50  
app/code/Comave/StripeOrderStatus/Block/Adminhtml/Grid/Column/StripeStatus.php
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,50 @@
<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Block\Adminhtml\Grid\Column;

use Magento\Backend\Block\Widget\Grid\Column\Renderer\AbstractRenderer;
use Magento\Framework\DataObject;
use Comave\StripeOrderStatus\Service\StripeStatusProcessor;
use Comave\StripeOrderStatus\Trait\StripeStatusRenderer;

/**
 * Renders the Stripe Payment Status column in the admin grid.
 */
class StripeStatus extends AbstractRenderer
{
    use StripeStatusRenderer;

    /**
     * @param StripeStatusProcessor $statusProcessor
     * @param \Magento\Backend\Block\Context $context
     * @param array $data
     */
    public function __construct(
        private readonly StripeStatusProcessor $statusProcessor,
        \Magento\Backend\Block\Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Renders the grid column.
     *
     * @param DataObject $row
     * @return string
     */
    public function render(DataObject $row): string
    {
        $orderId = (int)$row->getData($this->getColumn()->getIndex());
        $status = $this->getStripeStatusSafely($orderId, $this->statusProcessor);

        return $this->createStatusBadge(
            $status,
            $this->statusProcessor,
            fn($content) => $this->escapeHtml($content),
            fn($content) => $this->escapeHtmlAttr($content)
        );
    }
}
  113 changes: 113 additions & 0 deletions113  
app/code/Comave/StripeOrderStatus/Model/Source/StripePaymentStatus.php
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,113 @@
<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;

/**
 * Option source for Stripe payment statuses
 * 
 * Provides status mappings, labels, and CSS classes for Stripe payment statuses
 */
class StripePaymentStatus implements OptionSourceInterface
{
    /**
     * Status constants
     */
    public const STATUS_PENDING = 'Pending';
    public const STATUS_UNCAPTURED = 'Uncaptured';
    public const STATUS_SUCCEEDED = 'Succeeded';
    public const STATUS_CANCELED = 'Canceled';
    public const STATUS_NOT_STRIPE = 'not_stripe';
    public const STATUS_NO_INTENT = 'no_intent';
    public const STATUS_API_ERROR = 'api_error';
    public const STATUS_UNKNOWN = 'unknown';

    /**
     * Map Stripe API status to user-friendly status
     *
     * @param string $stripeStatus
     * @return string
     */
    public function mapStripeStatus(string $stripeStatus): string
    {
        return match ($stripeStatus) {
            'requires_payment_method', 'requires_confirmation', 'requires_action', 'processing', 'pending' => self::STATUS_PENDING,
            'requires_capture' => self::STATUS_UNCAPTURED,
            'succeeded' => self::STATUS_SUCCEEDED,
            'canceled', 'cancelled' => self::STATUS_CANCELED,
            default => ucwords(str_replace('_', ' ', $stripeStatus))
        };
    }

    /**
     * Get user-friendly label for status
     *
     * @param string $status
     * @return \Magento\Framework\Phrase
     */
    public function getStatusLabel(string $status): \Magento\Framework\Phrase
    {
        return match ($status) {
            self::STATUS_PENDING => __('Pending'),
            self::STATUS_UNCAPTURED => __('Uncaptured'),
            self::STATUS_SUCCEEDED => __('Succeeded'),
            self::STATUS_CANCELED => __('Canceled'),
            self::STATUS_NOT_STRIPE => __('Not Stripe'),
            self::STATUS_NO_INTENT => __('No Intent'),
            self::STATUS_API_ERROR => __('API Error'),
            default => __($status)
        };
    }

    /**
     * Get CSS class for status styling
     *
     * @param string $status
     * @return string
     */
    public function getStatusCssClass(string $status): string
    {
        return match ($status) {
            self::STATUS_SUCCEEDED => 'grid-severity-notice stripe-payment-status-succeeded',
            self::STATUS_UNCAPTURED => 'grid-severity-minor stripe-payment-status-uncaptured',
            self::STATUS_PENDING => 'grid-severity-minor stripe-payment-status-pending',
            self::STATUS_CANCELED => 'grid-severity-critical stripe-payment-status-canceled',
            self::STATUS_NOT_STRIPE => 'grid-severity-minor stripe-payment-status-not-stripe',
            self::STATUS_NO_INTENT => 'grid-severity-minor stripe-payment-status-no-intent',
            self::STATUS_API_ERROR => 'grid-severity-major stripe-payment-status-api-error',
            default => 'grid-severity-minor stripe-payment-status-unknown'
        };
    }

    /**
     * Check if status indicates an error condition
     *
     * @param string $status
     * @return bool
     */
    public function isErrorStatus(string $status): bool
    {
        return in_array($status, [self::STATUS_API_ERROR, self::STATUS_NO_INTENT], true);
    }

    /**
     * Get array of all available statuses for option source
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => self::STATUS_PENDING, 'label' => $this->getStatusLabel(self::STATUS_PENDING)],
            ['value' => self::STATUS_UNCAPTURED, 'label' => $this->getStatusLabel(self::STATUS_UNCAPTURED)],
            ['value' => self::STATUS_SUCCEEDED, 'label' => $this->getStatusLabel(self::STATUS_SUCCEEDED)],
            ['value' => self::STATUS_CANCELED, 'label' => $this->getStatusLabel(self::STATUS_CANCELED)],
            ['value' => self::STATUS_NOT_STRIPE, 'label' => $this->getStatusLabel(self::STATUS_NOT_STRIPE)],
            ['value' => self::STATUS_NO_INTENT, 'label' => $this->getStatusLabel(self::STATUS_NO_INTENT)],
            ['value' => self::STATUS_API_ERROR, 'label' => $this->getStatusLabel(self::STATUS_API_ERROR)],
        ];
    }
}
  140 changes: 140 additions & 0 deletions140  
app/code/Comave/StripeOrderStatus/Service/StripePaymentValidator.php
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,140 @@
<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Service;

use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

/**
 * Service class to validate Stripe payment data and extract payment intent IDs
 * 
 * This validator focuses on determining if an order has Stripe payment method
 * and extracting relevant payment identifiers from order payment data.
 */
class StripePaymentValidator
{
    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Check if order has Stripe payment method
     *
     * @param int $orderId
     * @return bool
     */
    public function hasStripePayment(int $orderId): bool
    {
        try {
            $order = $this->orderRepository->get($orderId);
            return $this->isStripePaymentMethod($order);
        } catch (NoSuchEntityException $e) {
            $this->logger->warning("Order not found: {$orderId}");
            return false;
        } catch (\Exception $e) {
            $this->logger->error("Error validating Stripe payment for order {$orderId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if order payment method is Stripe-based
     *
     * @param Order $order
     * @return bool
     */
    public function isStripePaymentMethod(Order $order): bool
    {
        $payment = $order->getPayment();
        if (!$payment) {
            return false;
        }

        $paymentMethod = $payment->getMethod();
        return str_contains($paymentMethod, 'stripe');
    }

    /**
     * Extract Stripe payment intent ID or transaction ID from order
     *
     * @param int $orderId
     * @return string|null
     */
    public function getStripeTransactionId(int $orderId): ?string
    {
        try {
            $order = $this->orderRepository->get($orderId);
            return $this->extractTransactionId($order);
        } catch (\Exception $e) {
            $this->logger->error("Error getting transaction ID for order {$orderId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract transaction ID from order payment
     *
     * @param Order $order
     * @return string|null
     */
    private function extractTransactionId(Order $order): ?string
    {
        $payment = $order->getPayment();
        if (!$payment) {
            return null;
        }

        // Try last transaction ID first
        $transactionId = $payment->getLastTransId();
        if (!empty($transactionId)) {
            return $this->cleanTransactionId($transactionId);
        }

        // Try additional information
        $additionalInfo = $payment->getAdditionalInformation();

        // Check various possible fields
        $possibleFields = [
            'payment_intent',
            'payment_intent_id',
            'last_charge_id',
            'checkout_session_id'
        ];

        foreach ($possibleFields as $field) {
            if (isset($additionalInfo[$field]) && !empty($additionalInfo[$field])) {
                return $this->cleanTransactionId($additionalInfo[$field]);
            }
        }

        return null;
    }

    /**
     * Clean and validate transaction ID format
     *
     * @param string $transactionId
     * @return string|null
     */
    private function cleanTransactionId(string $transactionId): ?string
    {
        $cleaned = trim($transactionId);

        // Validate Stripe ID format (payment intent or charge)
        if (preg_match('/^(pi_|ch_)[a-zA-Z0-9_]+$/', $cleaned)) {
            return $cleaned;
        }

        return null;
    }
}
  190 changes: 190 additions & 0 deletions190  
app/code/Comave/StripeOrderStatus/Service/StripeStatusProcessor.php
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,190 @@
<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Service;

use StripeIntegration\Payments\Model\Stripe\PaymentIntent;
use StripeIntegration\Payments\Helper\Token;
use Magento\Framework\App\CacheInterface;
use Psr\Log\LoggerInterface;
use Comave\StripeOrderStatus\Model\Source\StripePaymentStatus;

/**
 * Service class to process Stripe payment status and format display data
 * 
 * This processor handles Stripe API interactions, status mapping, 
 * and provides formatted display data with caching support.
 */
class StripeStatusProcessor
{
    private const CACHE_LIFETIME = 300; // 5 minutes
    private const CACHE_TAG = 'stripe_payment_status';

    /**
     * @var array<string, string> Runtime cache for status lookups
     */
    private array $statusCache = [];

    /**
     * @param PaymentIntent $paymentIntent
     * @param Token $tokenHelper
     * @param CacheInterface $cache
     * @param LoggerInterface $logger
     * @param StripePaymentValidator $validator
     * @param StripePaymentStatus $statusSource
     */
    public function __construct(
        private readonly PaymentIntent $paymentIntent,
        private readonly Token $tokenHelper,
        private readonly CacheInterface $cache,
        private readonly LoggerInterface $logger,
        private readonly StripePaymentValidator $validator,
        private readonly StripePaymentStatus $statusSource
    ) {
    }

    /**
     * Get Stripe payment status for order
     *
     * @param int $orderId
     * @return string
     */
    public function getStatusForOrder(int $orderId): string
    {
        $cacheKey = "stripe_status_{$orderId}";
        $cachedStatus = $this->cache->load($cacheKey);

        if ($cachedStatus !== false) {
            return $cachedStatus;
        }

        $status = $this->fetchStripeStatus($orderId);

        // Only cache successful responses, not errors
        if (!$this->statusSource->isErrorStatus($status)) {
            $this->cache->save($status, $cacheKey, [self::CACHE_TAG], self::CACHE_LIFETIME);
        }

        return $status;
    }

    /**
     * Fetch Stripe payment status from API
     *
     * @param int $orderId
     * @return string
     */
    private function fetchStripeStatus(int $orderId): string
    {
        if (!$this->validator->hasStripePayment($orderId)) {
anas-bcg marked this conversation as resolved.
            return StripePaymentStatus::STATUS_NOT_STRIPE;
        }

        $transactionId = $this->validator->getStripeTransactionId($orderId);
        if (!$transactionId) {
            return StripePaymentStatus::STATUS_NO_INTENT;
        }

        // Check runtime cache first
        if (isset($this->statusCache[$transactionId])) {
            return $this->statusCache[$transactionId];
        }

        try {
            $stripeObject = $this->retrieveStripeObject($transactionId);
            $status = $this->extractStatusFromObject($stripeObject);

            $formattedStatus = $this->statusSource->mapStripeStatus($status);
            $this->statusCache[$transactionId] = $formattedStatus;

            return $formattedStatus;
        } catch (\Exception $e) {
            $this->logger->error("Error fetching Stripe status for order {$orderId}: " . $e->getMessage());
            $this->statusCache[$transactionId] = StripePaymentStatus::STATUS_API_ERROR;
            return StripePaymentStatus::STATUS_API_ERROR;
        }
    }

    /**
     * Retrieve Stripe object from API
     *
     * @param string $transactionId
     * @return mixed
     */
    private function retrieveStripeObject(string $transactionId)
    {
        if (str_starts_with($transactionId, 'pi_')) {
            // Handle payment intent
            try {
                $paymentIntentModel = $this->paymentIntent->fromPaymentIntentId($transactionId);
                return $paymentIntentModel->getStripeObject();
            } catch (\Exception $e) {
                // If we can't retrieve but have valid ID, assume successful
                return (object)['status' => 'succeeded'];
            }
        } elseif (str_starts_with($transactionId, 'ch_')) {
            // Handle charge - try to get associated payment intent
            try {
                $intent = $this->paymentIntent->fromPaymentIntentId($transactionId, ['status']);
                return $intent->getStripeObject();
            } catch (\Exception $e) {
                // Assume successful for valid charge ID
                return (object)['status' => 'succeeded'];
            }
        }

        return null;
    }

    /**
     * Extract status from Stripe object
     *
     * @param mixed $stripeObject
     * @return string
     */
    private function extractStatusFromObject($stripeObject): string
    {
        if (!$stripeObject) {
            return 'unknown';
        }

        // Try different ways to get status
        if (isset($stripeObject->status)) {
            return $stripeObject->status;
        }

        if (isset($stripeObject->_values['status'])) {
            return $stripeObject->_values['status'];
        }

        if (method_exists($stripeObject, 'toArray')) {
            $array = $stripeObject->toArray();
            return $array['status'] ?? 'succeeded';
        }

        return 'succeeded'; // Default assumption
    }

    /**
     * Get user-friendly label for status
     *
     * @param string $status
     * @return \Magento\Framework\Phrase
     */
    public function getStatusLabel(string $status): \Magento\Framework\Phrase
    {
        return $this->statusSource->getStatusLabel($status);
    }

    /**
     * Get CSS class for status styling
     *
     * @param string $status
     * @return string
     */
    public function getStatusCssClass(string $status): string
    {
        return $this->statusSource->getStatusCssClass($status);
    }
}
  65 changes: 65 additions & 0 deletions65  
app/code/Comave/StripeOrderStatus/Trait/StripeStatusRenderer.php
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,65 @@
<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Trait;

use Comave\StripeOrderStatus\Service\StripeStatusProcessor;

/**
 * Trait for rendering Stripe payment status badges
 * 
 * Provides common functionality for creating HTML status badges
 * across different grid implementations (UI Component and Block).
 */
trait StripeStatusRenderer
{
    /**
     * Create HTML badge for status display
     *
     * @param string $status
     * @param StripeStatusProcessor $statusProcessor
     * @param callable|null $escapeHtml Optional HTML escaper function
     * @param callable|null $escapeHtmlAttr Optional HTML attribute escaper function
     * @return string
     */
    protected function createStatusBadge(
        string $status, 
        StripeStatusProcessor $statusProcessor,
        ?callable $escapeHtml = null,
        ?callable $escapeHtmlAttr = null
    ): string {
        $label = $statusProcessor->getStatusLabel($status);
        $cssClass = $statusProcessor->getStatusCssClass($status);

        // Use provided escapers or fallback to built-in
        $escapeHtml = $escapeHtml ?: fn($content) => htmlspecialchars($content, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
        $escapeHtmlAttr = $escapeHtmlAttr ?: $escapeHtml;

        return sprintf(
            '<span class="%s"><span>%s</span></span>',
            $escapeHtmlAttr($cssClass),
            $escapeHtml((string)$label)
        );
    }

    /**
     * Get Stripe status with error handling
     *
     * @param int $orderId
     * @param StripeStatusProcessor $statusProcessor
     * @return string
     */
    protected function getStripeStatusSafely(int $orderId, StripeStatusProcessor $statusProcessor): string
    {
        if ($orderId === 0) {
            return 'unknown';
        }

        try {
            return $statusProcessor->getStatusForOrder($orderId);
        } catch (\Exception $e) {
            return 'unknown';
        }
    }
}
  74 changes: 74 additions & 0 deletions74  
app/code/Comave/StripeOrderStatus/Ui/Component/Listing/Column/StripeStatus.php
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,74 @@
<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;
use Comave\StripeOrderStatus\Service\StripeStatusProcessor;
use Comave\StripeOrderStatus\Trait\StripeStatusRenderer;

/**
 * Stripe payment status column component for admin order grid
 * 
 * This component handles all business logic for retrieving, processing,
 * and formatting Stripe payment status data for display in the grid.
 * It integrates with the StripeStatusProcessor service to fetch status data.
 */
class StripeStatus extends Column
{
    use StripeStatusRenderer;
    /**
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param StripeStatusProcessor $statusProcessor
     * @param array<string, mixed> $components
     * @param array<string, mixed> $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        private readonly StripeStatusProcessor $statusProcessor,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare data source with Stripe payment status
     * 
     * This method handles all the business logic for fetching and formatting
     * Stripe status data for each order in the grid. It replaces the functionality
     * that was previously handled by the plugin.
     *
     * @param array<string, mixed> $dataSource
     * @return array<string, mixed>
     */
    public function prepareDataSource(array $dataSource): array
anas-bcg marked this conversation as resolved.
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                $item[$this->getData('name')] = $this->formatStripeStatus($item);
            }
        }

        return $dataSource;
    }

    /**
     * Format Stripe status for display including fetching data and creating badge
     *
     * @param array<string, mixed> $item
     * @return string
     */
    private function formatStripeStatus(array $item): string
    {
        $orderId = (int)($item['entity_id'] ?? 0);
        $status = $this->getStripeStatusSafely($orderId, $this->statusProcessor);

        return $this->createStatusBadge($status, $this->statusProcessor);
    }
}
  22 changes: 22 additions & 0 deletions22  
app/code/Comave/StripeOrderStatus/composer.json
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,22 @@
{
    "name": "comave/stripe-order-status",
    "description": "Magento 2 module to display Stripe payment status in the admin order grid",
    "type": "magento2-module",
    "license": "proprietary",
    "version": "1.0.1",
    "require": {
        "php": "^8.1|^8.2|^8.3",
        "magento/framework": "^103.0",
        "magento/module-sales": "^103.0",
        "magento/module-ui": "^101.2",
        "stripe/module-payments": "^4.0"
    },
    "autoload": {
        "files": [
            "registration.php"
        ],
        "psr-4": {
            "Comave\\StripeOrderStatus\\": ""
        }
    }
}
  11 changes: 11 additions & 0 deletions11  
app/code/Comave/StripeOrderStatus/etc/module.xml
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,11 @@
<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Module/etc/module.xsd">
    <module name="Comave_StripeOrderStatus" setup_version="1.0.1">
        <sequence>
            <module name="Magento_Sales"/>
            <module name="Magento_Ui"/>
            <module name="StripeIntegration_Payments"/>
        </sequence>
    </module>
</config>
  10 changes: 10 additions & 0 deletions10  
app/code/Comave/StripeOrderStatus/i18n/en_US.csv
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,10 @@
"Stripe Payment Status","Stripe Payment Status"
"Succeeded","Succeeded"
"Pending","Pending"
"Uncaptured","Uncaptured"
"Canceled","Canceled"
"Error","Error"
"API Error","API Error"
"Not Stripe","Not Stripe"
"No Payment","No Payment"
"No Intent","No Intent"
  11 changes: 11 additions & 0 deletions11  
app/code/Comave/StripeOrderStatus/registration.php
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,11 @@
<?php

declare(strict_types=1);

use Magento\Framework\Component\ComponentRegistrar;

ComponentRegistrar::register(
    ComponentRegistrar::MODULE,
    'Comave_StripeOrderStatus',
    __DIR__
);
  16 changes: 16 additions & 0 deletions16  
app/code/Comave/StripeOrderStatus/view/adminhtml/layout/sales_transactions_grid.xml
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,16 @@
<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="sales.transactions.grid.columnSet">
            <block class="Magento\Backend\Block\Widget\Grid\Column" name="sales.transactions.grid.columnSet.stripe_payment_status" as="stripe_payment_status">
                <arguments>
                    <argument name="header" xsi:type="string" translate="true">Stripe Payment Status</argument>
                    <argument name="index" xsi:type="string">increment_id</argument>
                    <argument name="renderer" xsi:type="string">Comave\StripeOrderStatus\Block\Adminhtml\Grid\Column\StripeStatus</argument>
                    <argument name="filter" xsi:type="boolean">false</argument>
                    <argument name="sortable" xsi:type="boolean">false</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
  16 changes: 16 additions & 0 deletions16  
app/code/Comave/StripeOrderStatus/view/adminhtml/layout/sales_transactions_grid_block.xml
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,16 @@
<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="sales.transactions.grid.columnSet">
            <block class="Magento\Backend\Block\Widget\Grid\Column" name="stripe_payment_status" as="stripe_payment_status">
                <arguments>
                    <argument name="header" xsi:type="string" translate="true">Stripe Payment Status</argument>
                    <argument name="index" xsi:type="string">order_id</argument>
                    <argument name="renderer" xsi:type="string">Comave\StripeOrderStatus\Block\Adminhtml\Grid\Column\StripeStatus</argument>
                    <argument name="filter" xsi:type="boolean">false</argument>
                    <argument name="sortable" xsi:type="boolean">false</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
  13 changes: 13 additions & 0 deletions13  
app/code/Comave/StripeOrderStatus/view/adminhtml/ui_component/sales_order_grid.xml
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,13 @@
<?xml version="1.0"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <columns name="sales_order_columns">
        <column name="stripe_payment_status" class="Comave\StripeOrderStatus\Ui\Component\Listing\Column\StripeStatus">
            <settings>
                <label translate="true">Stripe Payment Status</label>
                <sortable>false</sortable>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
            </settings>
        </column>
    </columns>
</listing>
  62 changes: 62 additions & 0 deletions62  
app/code/Comave/StripeOrderStatus/view/adminhtml/web/css/stripe-status.css
Viewed
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,62 @@
/* Grid-optimized badge styles for Stripe payment status */
.stripe-payment-status {
    display: inline-block;
}

.stripe-payment-status span {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    white-space: nowrap;
}

/* Success statuses - Green */
.grid-severity-notice span,
.stripe-payment-status-succeeded span {
    background-color: #28a745;
    color: #fff;
}

/* Warning statuses - Yellow */
.grid-severity-minor span,
.stripe-payment-status-pending span,
.stripe-payment-status-processing span {
    background-color: #ffc107;
    color: #212529;
}

/* Action required statuses - Orange */
.grid-severity-major span,
.stripe-payment-status-requires-action span,
.stripe-payment-status-requires-confirmation span,
.stripe-payment-status-requires-payment-method span {
    background-color: #fd7e14;
    color: #fff;
}

/* Error statuses - Red */
.grid-severity-critical span,
.stripe-payment-status-canceled span,
.stripe-payment-status-error span,
.stripe-payment-status-api-error span {
    background-color: #dc3545;
    color: #fff;
}

/* Neutral statuses - Gray */
.grid-severity-normal span,
.stripe-payment-status-not-stripe span {
    background-color: #6c757d;
    color: #fff;
}

/* Unknown/other statuses - Light gray */
.grid-severity-trivial span,
.stripe-payment-status-unknown span,
.stripe-payment-status-no-payment span,
.stripe-payment-status-no-intent span {
    background-color: #e9ecef;
    color: #495057;
}
   3 changes: 2 additions & 1 deletion3  
app/etc/config.php
Viewed
Original file line number	Diff line number	Diff line change
@@ -870,6 +870,7 @@
        'Comave_SportsclubFan' => 1,
        'Comave_StoreConfig' => 1,
        'StripeIntegration_Tax' => 1,
        'StripeIntegration_Payments' => 1,
        'Comave_StripeTax' => 0,
        'Webkul_MpEasyPost' => 0,
        'Comave_TravellerInfo' => 1,
@@ -897,8 +898,8 @@
        'PayPal_BraintreeGiftCardAccount' => 0,
        'PayPal_BraintreeGiftWrapping' => 0,
        'PayPal_BraintreeGraphQl' => 1,
        'StripeIntegration_Payments' => 1,
        'Comave_StripeGraphQl' => 1,
        'Comave_StripeOrderStatus' => 1,
        'Comave_BigBuyShipping' => 1,
        'Webkul_Customoption' => 0,
        'Webkul_FirebaseOTPLogin' => 1,